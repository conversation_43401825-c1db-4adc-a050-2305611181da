import 'server-only'

import { action } from '@/lib/actions/action'
import { kv } from '@vercel/kv'
import fetch from 'node-fetch'
import { z } from 'zod'

import { getHttpsAgentSignedWithCertAndKey } from '@/helpers/certificate/getHttpsAgentSignedWithCertAndKey'

const schema = z.object({
	access_token: z.string().transform(token => {
		if (!token.startsWith('Bearer')) token = `Bearer ${token}`
		return token
	}),
	expires_in: z.number(),
	scope: z.literal('pagamento-pix.write boleto-cobranca.read boleto-cobranca.write'),
	token_type: z.literal('Bearer'),
})

export const bancoInterGetToken = action

	.input(z.object({ customerId: z.string().uuid() }))

	.output(schema)

	.handler(async ({ input: { customerId } }) => {
		// check for an existing token in Vercel KV Cache
		const cacheKey = `BANCO_INTER_TOKEN_CLIENT_ID=${customerId}`
		const cached = await kv.get(cacheKey)
		if (cached) return schema.parse(cached)

		const { get } = await import('@/helpers/secrets/get')

		const [clientId, clientIdError] = await get({ customerId, type: 'banking-banco-inter-client-id' })
		const [clientSecret, clientSecretError] = await get({ customerId, type: 'banking-banco-inter-client-secret' })
		const [certificate, certificateError] = await get({ customerId, type: 'banking-banco-inter-certificate' })
		const [certificateSecret, certificateSecretError] = await get({ customerId, type: 'banking-banco-inter-certificate-secret' })

		if (!clientId?.value || !clientSecret?.value || !certificate?.value || !certificateSecret?.value) {
			throw new Error('Missing secrets', { cause: clientIdError || clientSecretError || certificateError || certificateSecretError })
		}

		const certificateString = Buffer.from(certificate.value, 'base64').toString('utf-8')
		const certificateSecretString = Buffer.from(certificateSecret.value, 'base64').toString('utf-8')

		const headers = { 'Content-Type': 'application/x-www-form-urlencoded' }

		const body = new URLSearchParams()
		body.append('client_id', clientId.value)
		body.append('client_secret', clientSecret.value)
		body.append('grant_type', 'client_credentials')
		body.append('scope', 'pagamento-pix.write boleto-cobranca.read boleto-cobranca.write')

		const [agent, error] = await getHttpsAgentSignedWithCertAndKey({
			certificate: certificateString,
			secret: certificateSecretString,
		})
		if (!agent) throw new Error(`Error getting HTTPS agent with signed certificate: ${error.message}`)

		const req = await fetch('https://cdpj.partners.bancointer.com.br/oauth/v2/token', {
			agent: agent,
			body,
			headers,
			method: 'POST',
		})

		const urlencoded = new URLSearchParams()
		urlencoded.append('grant_type', 'client_credentials')

		const res = await req.json()

		const data = schema.parse(res)

		// add data to Vercel KV Cache
		await kv.set(cacheKey, data, { ex: data.expires_in })

		return data
	})
