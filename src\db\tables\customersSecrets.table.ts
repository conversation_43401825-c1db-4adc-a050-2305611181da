import { customersSecretsTypesMapping } from '@/src/db/tables/customersSecrets.helpers'
import { BaseTable } from '../baseTable'

export class CustomersSecretsTable extends BaseTable {
	readonly table = 'customers_secrets'

	columns = this.setColumns(
		t => ({
			customerId: t.name('customer_id').uuid().foreignKey('customers', 'id', {
				name: 'customers_secrets_customer_id_customers_id_fk',
				onUpdate: 'CASCADE',
				onDelete: 'CASCADE',
			}),
			expiration: t.timestampNoTZ().nullable(),
			type: t.enum('public.customers_secrets_types_enum', customersSecretsTypesMapping),
		}),
		t => t.primaryKey(['customerId', 'type'], 'customers_secrets_customer_id_type_pk'),
	)

	computed = this.setComputed(({ computeBatchAtRuntime }) => ({
		data: computeBatchAtRuntime(['customerId', 'type'], async customersSecrets => {
			const { get } = await import('@/helpers/secrets/get')

			const data = await Promise.all(
				customersSecrets.map(async ({ customerId, type }) => {
					const [secret, secretError] = await get({ customerId, type })
					if (!secret?.value) throw secretError

					return secret.value
				}),
			)

			return data
		}),
	}))
}
