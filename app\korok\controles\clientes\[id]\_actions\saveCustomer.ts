'use server'

import { action } from '@/lib/actions/action'
import { disableCustomersOnInvoicy } from '@/lib/invoicy/disableCustomersOnInvoicy'
import { saveCustomersOnInvoicy } from '@/lib/invoicy/saveCustomersOnInvoicy'
import { saveCustomersOnRocketChat } from '@/lib/rocketchat/saveCustomersOnRocketChat'
import { saveCustomersOnSeafile } from '@/lib/seafile/saveCustomersOnSeafile'
import { db } from '@/src/db/db'
import { CustomersInputSchema } from '@/src/db/tables/customers.schemas'

export const saveCustomer = action

	.input(CustomersInputSchema)

	.handler(async ({ input }) => {
		const { nfseSettings, secrets, taxesConfigs, taxPasswords, taxTimelines, partners, payrollConfigs, taxNumbers, ...customerData } = input

		const customerId = customerData.id

		const secretsWithoutData = secrets.map(({ data, ...secret }) => ({ ...secret, customerId }))

		// Upload secrets (needs to be done before creating or updating the customer)
		const { put } = await import('@/helpers/secrets/put')

		for (const { type, data } of secrets) {
			const [savedSecret, savedSecretError] = await put({ customerId, type, data })
			if (!savedSecret) throw savedSecretError
		}

		// create main record
		await db.customers.create(customerData).onConflict('id').merge()

		// create related records
		const result = await db.customers
			.find(customerId)
			.update({
				taxNumbers: { delete: { customerId } },
				nfseSettings: { delete: { customerId } },
				partners: { delete: { customerId } },
				payrollConfigs: { delete: { customerId } },
				taxesConfigs: { delete: { customerId } },
				taxTimelines: { delete: { customerId } },
				taxPasswords: { delete: { customerId } },
				secrets: { delete: { customerId } },
			})
			.update({
				nfseSettings: { create: nfseSettings },
				partners: { create: partners },
				payrollConfigs: { create: payrollConfigs },
				taxesConfigs: { create: taxesConfigs },
				taxNumbers: { create: taxNumbers },
				taxTimelines: { create: taxTimelines },
				taxPasswords: { create: taxPasswords },
				secrets: { create: secretsWithoutData },
			})

		// Create groups and departments on RocketChat (no need to do for each customer, only once)
		const [
			[rocketchatData, rocketchatError], //
			[invoicyData, invoicyError],
			[seafileData, seafileError],
			[disableInvoicyData, disableInvoicyError],
		] = await Promise.all([saveCustomersOnRocketChat(), saveCustomersOnInvoicy(), saveCustomersOnSeafile(), disableCustomersOnInvoicy()])

		if (!rocketchatData) throw rocketchatError
		if (!invoicyData) throw invoicyError
		if (!seafileData) throw seafileError
		if (!disableInvoicyData) throw disableInvoicyError

		return result
	})
