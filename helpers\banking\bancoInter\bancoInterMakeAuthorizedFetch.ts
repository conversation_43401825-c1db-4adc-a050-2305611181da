import { getHttpsAgentSignedWithCertAndKey } from '@/helpers/certificate/getHttpsAgentSignedWithCertAndKey'
import { action } from '@/lib/actions/action'
import fetch from 'node-fetch'
import type { RequestInit } from 'node-fetch'
import { z } from 'zod'
import { bancoInterGetToken } from './bancoInterGetToken'

export const bancoInterMakeAuthorizedFetch = action

	.input(
		z.object({
			customerId: z.string().uuid(),
			url: z.string().url(),
			requestOptions: z.any().optional() as z.ZodType<RequestInit | undefined>,
		}),
	)

	.handler(async ({ input: { customerId, url, requestOptions } }) => {
		const { get } = await import('@/helpers/secrets/get')

		const [certificate, certificateError] = await get({ customerId, type: 'banking-banco-inter-certificate' })
		const [certificateSecret, certificateSecretError] = await get({ customerId, type: 'banking-banco-inter-certificate-secret' })

		if (!certificate?.value || !certificateSecret?.value) {
			throw new Error('Missing secrets', { cause: certificateError || certificateSecretError })
		}

		const certificateString = Buffer.from(certificate.value, 'base64').toString('utf-8')
		const certificateSecretString = Buffer.from(certificateSecret.value, 'base64').toString('utf-8')

		const [token, tokenError] = await bancoInterGetToken({ customerId })
		if (!token) throw tokenError

		const [agent, error] = await getHttpsAgentSignedWithCertAndKey({
			certificate: certificateString,
			secret: certificateSecretString,
		})
		if (!agent) throw error

		return fetch(url, {
			...requestOptions,
			agent,
			headers: {
				...requestOptions?.headers,
				Authorization: token.access_token,
				'Content-Type': 'application/json',
			},
		})
	})
